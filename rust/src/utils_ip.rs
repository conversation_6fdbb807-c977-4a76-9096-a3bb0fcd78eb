use std::net::{
    Ip<PERSON>ddr,
    Ipv4<PERSON>ddr,
    Ipv6Addr,
};
use std::str::FromStr;

use regex::Regex;

pub fn is_cidr(string: &str) -> bool {
    // Check if string contains a slash followed by digits
    if !string.contains('/') {
        return false;
    }

    let parts: Vec<&str> = string.split('/').collect();
    if parts.len() != 2 {
        return false;
    }

    let ip_part = parts[0];
    let prefix_part = parts[1];

    // Validate prefix part is a valid number
    let prefix = match prefix_part.parse::<u8>() {
        Ok(p) => p,
        Err(_) => return false,
    };

    // Check if IP part is valid IPv4 or IPv6
    if is_ip_v4(ip_part) {
        // IPv4 prefix must be between 0 and 32
        prefix <= 32
    } else if is_ip_v6(ip_part) {
        // IPv6 prefix must be between 0 and 128
        prefix <= 128
    } else {
        false
    }
}

pub fn is_ip(string: &str) -> bool {
    is_ip_v4(string) || is_ip_v6(string)
}

pub fn is_ip_v4(string: &str) -> bool {
    let parts: Vec<&str> = string.split('.').collect();
    if parts.len() != 4 {
        return false;
    }

    for part in parts {
        if let Ok(num) = part.parse::<u16>() {
            if num > 255 {
                return false;
            }
        } else {
            return false;
        }
    }

    true
}

pub fn is_ip_v6(string: &str) -> bool {
    let pattern = r"^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]+|::(ffff(:0{1,4})?:)?((25[0-5]|(2[0-4]|1?[0-9])?[0-9])\.){3}(25[0-5]|(2[0-4]|1?[0-9])?[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1?[0-9])?[0-9])\.){3}(25[0-5]|(2[0-4]|1?[0-9])?[0-9]))$";
    
    match Regex::new(pattern) {
        Ok(re) => re.is_match(string),
        Err(_) => false,
    }
}

pub fn is_private(string: &str) -> bool {
    is_private_v4(string) || is_private_v6(string)
}

pub fn is_private_v4(string: &str) -> bool {
    if !is_ip_v4(string) {
        return false;
    }

    if string.starts_with("10.") || string.starts_with("192.168.") {
        return true;
    }

    if string.starts_with("172.") {
        let parts: Vec<&str> = string.split('.').collect();
        if let Ok(second_octet) = parts[1].parse::<u8>() {
            return (16..=31).contains(&second_octet);
        }
    }

    false
}

pub fn is_private_v6(string: &str) -> bool {
    if !is_ip_v6(string) {
        return false;
    }

    // Convert to lowercase for consistent comparison
    let string = string.to_lowercase();

    // Check for loopback address (::1)
    if string == "::1" {
        return true;
    }

    // Check for Unique Local Addresses (ULA) - fc00::/7
    if string.starts_with("fc") || string.starts_with("fd") {
        return true;
    }

    // Check for Link-Local Addresses - fe80::/10
    if string.starts_with("fe80:") {
        return true;
    }

    false
}

pub fn get_hosts(ip: &str) -> Vec<String> {
    use std::net::IpAddr;

    // Parse the CIDR notation
    if !ip.contains('/') {
        return vec![];
    }

    let parts: Vec<&str> = ip.split('/').collect();
    if parts.len() != 2 {
        return vec![];
    }

    let ip_part = parts[0];
    let prefix_len = match parts[1].parse::<u8>() {
        Ok(p) => p,
        Err(_) => return vec![],
    };

    // Parse the IP address
    let addr = match ip_part.parse::<IpAddr>() {
        Ok(addr) => addr,
        Err(_) => return vec![],
    };

    match addr {
        IpAddr::V4(ipv4) => _get_hosts_ipv4(ipv4, prefix_len),
        IpAddr::V6(ipv6) => _get_hosts_ipv6(ipv6, prefix_len),
    }
}

fn _get_hosts_ipv4(network: Ipv4Addr, prefix_len: u8) -> Vec<String> {
    if prefix_len > 32 {
        return vec![];
    }

    let network_u32 = u32::from(network);
    let host_bits = 32 - prefix_len;
    let num_hosts = if host_bits >= 31 { 0 } else { (1u32 << host_bits) - 2 };

    if prefix_len == 32 {
        return vec![network.to_string()];
    }

    if num_hosts == 0 || num_hosts > 65536 {
        return vec![];
    }

    let network_mask = !((1u32 << host_bits) - 1);
    let network_addr = network_u32 & network_mask;

    let mut hosts = Vec::new();
    for i in 1..=num_hosts {
        let host_addr = network_addr + i;
        hosts.push(Ipv4Addr::from(host_addr).to_string());
    }

    hosts
}

fn _get_hosts_ipv6(network: Ipv6Addr, prefix_len: u8) -> Vec<String> {
    if prefix_len > 128 {
        return vec![];
    }

    if prefix_len == 128 {
        return vec![network.to_string()];
    }

    let host_bits = 128 - prefix_len;
    if host_bits > 16 {
        return vec![];
    }

    let network_u128 = u128::from(network);
    let num_hosts = if host_bits >= 63 { 0 } else { (1u128 << host_bits) - 2 };

    if num_hosts == 0 || num_hosts > 65536 {
        return vec![];
    }

    let network_mask = !((1u128 << host_bits) - 1);
    let network_addr = network_u128 & network_mask;

    let mut hosts = Vec::new();
    for i in 1..=num_hosts {
        let host_addr = network_addr + i;
        hosts.push(Ipv6Addr::from(host_addr).to_string());
    }

    hosts
}
